[20:06:07] [ServerMain/INFO]: [bootstrap] Running Java 21 (OpenJDK 64-Bit Server VM 21.0.7+6-LTS; Eclipse Adoptium Temurin-21.0.7+6) on Windows 11 10.0 (amd64)
[20:06:07] [ServerMain/INFO]: [bootstrap] Loading Paper 1.21.8-27-main@a664311 (2025-08-07T17:06:45Z) for Minecraft 1.21.8
[20:06:08] [ServerMain/INFO]: [PluginInitializerManager] Initializing plugins...
[20:06:08] [ServerMain/INFO]: [PluginInitializerManager] Initialized 0 plugins
[20:06:14] [ServerMain/INFO]: Environment: Environment[sessionHost=https://sessionserver.mojang.com, servicesHost=https://api.minecraftservices.com, name=PROD]
[20:06:14] [ServerMain/INFO]: Found new data pack file/bukkit, loading it automatically
[20:06:14] [ServerMain/INFO]: Found new data pack paper, loading it automatically
[20:06:15] [ServerMain/INFO]: No existing world data, creating new world
[20:06:16] [ServerMain/INFO]: Loaded 1407 recipes
[20:06:16] [ServerMain/INFO]: Loaded 1520 advancements
[20:06:16] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Initialising converters for DataConverter...
[20:06:17] [ServerMain/INFO]: [ca.spottedleaf.dataconverter.minecraft.datatypes.MCTypeRegistry] Finished initialising converters for DataConverter in 426.0ms
[20:06:17] [Server thread/INFO]: Starting minecraft server version 1.21.8
[20:06:17] [Server thread/INFO]: Loading properties
[20:06:17] [Server thread/INFO]: This server is running Paper version 1.21.8-27-main@a664311 (2025-08-07T17:06:45Z) (Implementing API version 1.21.8-R0.1-SNAPSHOT)
[20:06:17] [Server thread/INFO]: [spark] This server bundles the spark profiler. For more information please visit https://docs.papermc.io/paper/profiling
[20:06:17] [Server thread/INFO]: Server Ping Player Sample Count: 12
[20:06:17] [Server thread/INFO]: Using 4 threads for Netty based IO
[20:06:18] [Server thread/INFO]: [MoonriseCommon] Paper is using 4 worker threads, 1 I/O threads
[20:06:18] [Server thread/INFO]: [ChunkTaskScheduler] Chunk system is using population gen parallelism: true
[20:06:18] [Server thread/INFO]: Default game type: SURVIVAL
[20:06:18] [Server thread/INFO]: Generating keypair
[20:06:18] [Server thread/INFO]: Starting Minecraft server on *:25566
[20:06:18] [Server thread/INFO]: Using default channel type
[20:06:18] [Server thread/INFO]: Paper: Using Java compression from Velocity.
[20:06:18] [Server thread/INFO]: Paper: Using Java cipher from Velocity.
[20:06:18] [Server thread/INFO]: Preparing level "world"
[20:06:23] [Server thread/INFO]: Preparing start region for dimension minecraft:overworld
[20:06:23] [Server thread/INFO]: Preparing spawn area: 0%
[20:06:23] [Server thread/INFO]: Preparing spawn area: 2%
[20:06:24] [Server thread/INFO]: Preparing spawn area: 4%
[20:06:24] [Server thread/INFO]: Preparing spawn area: 18%
[20:06:25] [Server thread/INFO]: Preparing spawn area: 18%
[20:06:25] [Server thread/INFO]: Preparing spawn area: 26%
[20:06:26] [Server thread/INFO]: Preparing spawn area: 34%
[20:06:26] [Server thread/INFO]: Time elapsed: 3197 ms
[20:06:26] [Server thread/INFO]: Preparing start region for dimension minecraft:the_nether
[20:06:26] [Server thread/INFO]: Preparing spawn area: 2%
[20:06:26] [Server thread/INFO]: Preparing spawn area: 75%
[20:06:26] [Server thread/INFO]: Time elapsed: 529 ms
[20:06:26] [Server thread/INFO]: Preparing start region for dimension minecraft:the_end
[20:06:26] [Server thread/INFO]: Preparing spawn area: 2%
[20:06:27] [Server thread/INFO]: Time elapsed: 273 ms
[20:06:27] [Server thread/INFO]: [spark] Starting background profiler...
[20:06:27] [Server thread/INFO]: [spark] The async-profiler engine is not supported for your os/arch (windows11/amd64), so the built-in Java engine will be used instead.
[20:06:27] [Server thread/INFO]: Done preparing level "world" (8.706s)
[20:06:27] [Server thread/INFO]: Running delayed init tasks
[20:06:27] [Server thread/INFO]: Done (21.196s)! For help, type "help"
[20:06:27] [Server thread/INFO]: *************************************************************************************
[20:06:27] [Server thread/INFO]: This is the first time you're starting this server.
[20:06:27] [Server thread/INFO]: It's recommended you read our 'Getting Started' documentation for guidance.
[20:06:27] [Server thread/INFO]: View this and more helpful information here: https://docs.papermc.io/paper/next-steps
[20:06:27] [Server thread/INFO]: *************************************************************************************
