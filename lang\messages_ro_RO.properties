#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=You are already connected to this server\!
velocity.error.already-connected-proxy=You are already connected to this proxy\!
velocity.error.already-connecting=You are already trying to connect to a server\!
velocity.error.cant-connect=Unable to connect to {0}\: {1}
velocity.error.connecting-server-error=Unable to connect you to {0}. Please try again later.
velocity.error.connected-server-error=Your connection to {0} encountered a problem.
velocity.error.internal-server-connection-error=An internal server connection error occurred.
velocity.error.logging-in-too-fast=You are logging in too fast, try again later.
velocity.error.online-mode-only=You are not logged into your Minecraft account. If you are logged into your Minecraft account, try restarting your Minecraft client.
velocity.error.player-connection-error=An internal error occurred in your connection.
velocity.error.modern-forwarding-needs-new-client=This server is only compatible with Minecraft 1.13 and above.
velocity.error.modern-forwarding-failed=Your server did not send a forwarding request to the proxy. Make sure the server is configured for Velocity forwarding.
velocity.error.moved-to-new-server=You were kicked from {0}\: {1}
velocity.error.no-available-servers=There are no available servers to connect you to. Try again later or contact an admin.
velocity.error.illegal-chat-characters=Illegal characters in chat
# Commands
velocity.command.generic-error=An error occurred while running this command.
velocity.command.command-does-not-exist=This command does not exist.
velocity.command.players-only=Only players can run this command.
velocity.command.server-does-not-exist=The specified server {0} does not exist.
velocity.command.player-not-found=The specified player {0} does not exist.
velocity.command.server-current-server=You are currently connected to {0}.
velocity.command.server-too-many=There are too many servers set up. Use tab completion to view all servers available.
velocity.command.server-available=Available servers\:
velocity.command.server-tooltip-player-online={0} player online
velocity.command.server-tooltip-players-online={0} players online
velocity.command.server-tooltip-current-server=Currently connected to this server
velocity.command.server-tooltip-offer-connect-server=Click to connect to this server
velocity.command.glist-player-singular={0} player is currently connected to the proxy.
velocity.command.glist-player-plural={0} players are currently connected to the proxy.
velocity.command.glist-view-all=To view all players on servers, use /glist all.
velocity.command.reload-success=Velocity configuration successfully reloaded.
velocity.command.reload-failure=Unable to reload your Velocity configuration. Check the console for more details.
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1} is licensed under the terms of the GNU General Public License v3.
velocity.command.no-plugins=There are no plugins currently installed.
velocity.command.plugins-list=Plugins\: {0}
velocity.command.plugin-tooltip-website=Website\: {0}
velocity.command.plugin-tooltip-author=Author\: {0}
velocity.command.plugin-tooltip-authors=Authors\: {0}
velocity.command.dump-uploading=Uploading gathered information...
velocity.command.dump-send-error=An error occurred while communicating with the Velocity servers. The servers may be temporarily unavailable or there is an issue with your network settings. You can find more information in the log or console of your Velocity server.
velocity.command.dump-success=Created an anonymised report containing useful information about this proxy. If a developer requested it, you may share the following link with them\:
velocity.command.dump-will-expire=This link will expire in a few days.
velocity.command.dump-server-error=An error occurred on the Velocity servers and the dump could not be completed. Please contact the Velocity staff about this problem and provide the details about this error from the Velocity console or server log.
velocity.command.dump-offline=Likely cause\: Invalid system DNS settings or no internet connection
velocity.command.send-usage=/send <player> <server>
# Kick
velocity.kick.shutdown=Proxy se închide.