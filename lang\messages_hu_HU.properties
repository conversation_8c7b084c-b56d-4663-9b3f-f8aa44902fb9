#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=<PERSON><PERSON>r csatlakozva vagy ehhez a szerverhez\!
velocity.error.already-connected-proxy=<PERSON><PERSON>r csatlakozva vagy ehhez a proxyhoz\!
velocity.error.already-connecting=Jelenleg is csatlakozol egy szerverre\!
velocity.error.cant-connect=Nem lehet csatlakozni a(z) {0} szerverhez\: {1}
velocity.error.connecting-server-error=Nem tudunk csatlakoztatni a(z) {0} szerverhez. Kérlek próbáld újra később.
velocity.error.connected-server-error=A kapcsolatod a(z) {0} szerverhez hibába ütközött.
velocity.error.internal-server-connection-error=Egy szerveroldali hiba történt.
velocity.error.logging-in-too-fast=Túl gyorsan próbálsz csatlakozni, próbáld újra később.
velocity.error.online-mode-only=Nem vagy belépve a Minecraft profilodba. Ha mégis be vagy lépve, kérlek próbáld újraindítani a Minecraft kliensedet.
velocity.error.player-connection-error=Egy belső hiba keletkezett a kapcsolatodban.
velocity.error.modern-forwarding-needs-new-client=Ez a szerver csak a Minecraft 1.13 és afölötti verziókkal kompatibilis.
velocity.error.modern-forwarding-failed=A szerver ahonnan csatlakoztál nem küldött modern átirányítási kérelmet a proxy felé. Bizonyosodj meg róla, hogy a szerver be van állítva a modern Velocity átirányítás használatára.
velocity.error.moved-to-new-server=Ki lettél rúgva a(z) {0} szerverről\: {1}
velocity.error.no-available-servers=Jelenleg nincs elérhető szerver ahová csatlakoztatni tudnánk. Próbáld újra később, vagy lépj kapcsolatba egy adminisztrátorral.
velocity.error.illegal-chat-characters=Illegal characters in chat
# Commands
velocity.command.generic-error=Hiba történt a parancs futtatása közben.
velocity.command.command-does-not-exist=Ilyen parancs nem létezik.
velocity.command.players-only=Ezt a parancsot csak játékosok használhatják.
velocity.command.server-does-not-exist=A megadott szerver ({0}) nem létezik.
velocity.command.player-not-found=The specified player {0} does not exist.
velocity.command.server-current-server=Jelenleg a(z) {0} szerveren tartózkodsz.
velocity.command.server-too-many=Túl sok szerver van beállítva. Használd a tab parancs befejező funkciót, hogy megnézd az összes elérhető szervert.
velocity.command.server-available=Elérhető szerverek\:
velocity.command.server-tooltip-player-online={0} játékos online
velocity.command.server-tooltip-players-online={0} játékos online
velocity.command.server-tooltip-current-server=Jelenleg ezen a szerveren tartózkodsz
velocity.command.server-tooltip-offer-connect-server=Kattints ide, hogy csatlakozz erre a szerverre
velocity.command.glist-player-singular={0} játékos van jelenleg csatlakozva a proxyhoz.
velocity.command.glist-player-plural={0} játékos van jelenleg csatlakozva a proxyhoz.
velocity.command.glist-view-all=Hogy megnézd az összes játékost az összes szerveren, használd a /glist all parancsot.
velocity.command.reload-success=A Velocity beállításai sikeresen frissítve lettek.
velocity.command.reload-failure=Hiba történt a Velocity beállításainak frissítése közben. Több információt a konzolban találsz.
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1} licenszelve van a GNU General Public License v3 alatt.
velocity.command.no-plugins=Jelenleg egyetlen plugin sincs telepítve.
velocity.command.plugins-list=Pluginok\: {0}
velocity.command.plugin-tooltip-website=Weboldal\: {0}
velocity.command.plugin-tooltip-author=Készítő\: {0}
velocity.command.plugin-tooltip-authors=Készítők\: {0}
velocity.command.dump-uploading=Az összegyűjtött információ feltöltése...
velocity.command.dump-send-error=Egy hiba lépett fel miközben kommunikálni próbáltunk a Velocity szerverekkel. Lehetséges, hogy a szerver(ek) ideiglenesen elérhetetlenek, vagy ez egy hiba a te hálózati beállításaiddal. Több információt a logban, vagy a Velocity konzoljában találsz.
velocity.command.dump-success=Egy anonimizált jelentés sikeresen el lett készítve erről a proxyról. Ha egy fejlesztő kérte, akkor innen kimásolhatod a linket, és megoszthatod velük\: 
velocity.command.dump-will-expire=Ez a link egy pár napon belül le fog járni.
velocity.command.dump-server-error=Egy hiba lépett fel a Velocity szervereken, és a jelentést nem tudtuk elkészíteni. Kérlek lépj kapcsolatba egy Velocity személyzeti taggal ezzel a problémával kapcsolatban, és adj információt ezzel a hibával kapcsolatban a Velocity logokból, vagy a szerver konzolból.
velocity.command.dump-offline=Valószínűleg a következő a hiba okozója\: Hibás DNS beállítások, vagy nincs internetkapcsolat.
velocity.command.send-usage=/send <player> <server>
# Kick
velocity.kick.shutdown=Proxy shutting down.