#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=您已經連線到此伺服器了！
velocity.error.already-connected-proxy=您已經連線到此代理伺服器了！
velocity.error.already-connecting=您已經在嘗試連線伺服器了！
velocity.error.cant-connect=無法連線到 {0}：{1}
velocity.error.connecting-server-error=無法將您連線至 {0}，請稍後再試。
velocity.error.connected-server-error=您與 {0} 的連線出現問題。
velocity.error.internal-server-connection-error=發送內部伺服器連線錯誤。
velocity.error.logging-in-too-fast=您的登入過於頻繁，請稍後再試。
velocity.error.online-mode-only=您尚未登入至 Minecraft 帳號。若您已登入，請嘗試重啟您的啟動器。
velocity.error.player-connection-error=您的連線發生內部錯誤。
velocity.error.modern-forwarding-needs-new-client=此伺服器僅相容 Minecraft 1.13 及更高版本。
velocity.error.modern-forwarding-failed=您的伺服器未向代理伺服器轉發請求，請確保已配置 Velocity 轉發。
velocity.error.moved-to-new-server=您已被踢出 {0}：{1}
velocity.error.no-available-servers=您當前沒有可連線的伺服器，請稍後重試或聯絡管理員。
velocity.error.illegal-chat-characters=聊天欄中出現不允許的字符
# Commands
velocity.command.generic-error=執行此指令時發生錯誤。
velocity.command.command-does-not-exist=此指令不存在。
velocity.command.players-only=只有玩家才能執行這個指令。
velocity.command.server-does-not-exist=指定的伺服器 {0} 不存在。
velocity.command.player-not-found=指定的玩家 {0} 不存在。
velocity.command.server-current-server=您已連線到 {0}。
velocity.command.server-too-many=設定的伺服器過多，請使用 Tab 鍵補全來查看所有可用的伺服器。
velocity.command.server-available=可用的伺服器：
velocity.command.server-tooltip-player-online={0} 名玩家在線
velocity.command.server-tooltip-players-online={0} 名玩家在線
velocity.command.server-tooltip-current-server=當前已連線至此伺服器
velocity.command.server-tooltip-offer-connect-server=點擊連線至此伺服器
velocity.command.glist-player-singular=共有 {0} 名玩家已連線至此代理伺服器。
velocity.command.glist-player-plural=共有 {0} 名玩家已連線至此代理伺服器。
velocity.command.glist-view-all=使用 /glist all 命令來查看所有伺服器的全部玩家列表。
velocity.command.reload-success=Velocity 配置已成功重載。
velocity.command.reload-failure=無法重載 Velocity 配置，請查看控制台了解詳情。
velocity.command.version-copyright=Copyright 2018-2023 {0}。{1} 以 GNU 通用公共許可證第三版授權。
velocity.command.no-plugins=當前沒有安裝任何插件。
velocity.command.plugins-list=插件：{0}
velocity.command.plugin-tooltip-website=網站：{0}
velocity.command.plugin-tooltip-author=作者：{0}
velocity.command.plugin-tooltip-authors=作者：{0}
velocity.command.dump-uploading=正在上傳已收集的訊息...
velocity.command.dump-send-error=與 Velocity 伺服器通訊時發生錯誤，伺服器可能暫不可用或您的網路設置存在問題。您可在 Velocity 伺服器日誌或控制台中了解詳情。
velocity.command.dump-success=已創建關於此代理的匿名反饋資料。若開發者需要，您可與其分享以下連結：
velocity.command.dump-will-expire=此連結將於幾天後過期。
velocity.command.dump-server-error=Velocity 伺服器發生錯誤，無法完成記憶體轉存。請聯絡 Velocity 開發者並從 Velocity 控制台或日誌中提供詳細錯誤訊息。
velocity.command.dump-offline=可能原因：系統 DNS 設置無效或無網路連線
velocity.command.send-usage=/send <玩家> <伺服器>
# Kick
velocity.kick.shutdown=正在關閉代理伺服器。