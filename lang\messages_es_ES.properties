#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=¡Ya estás conectado a este servidor\!
velocity.error.already-connected-proxy=¡Ya estás conectado a este proxy\!
velocity.error.already-connecting=¡Ya estás intentando conectarte a un servidor\!
velocity.error.cant-connect=No se ha podido conectar a {0}\: {1}
velocity.error.connecting-server-error=No hemos podido conectarte a {0}. Por favor, inténtalo de nuevo más tarde.
velocity.error.connected-server-error=Tu conexión a {0} ha sufrido un problema.
velocity.error.internal-server-connection-error=Se ha producido un error interno en la conexión al servidor.
velocity.error.logging-in-too-fast=Estás iniciando sesión demasiado rápido, inténtalo de nuevo más tarde.
velocity.error.online-mode-only=No has iniciado sesión con tu cuenta de Minecraft. Si crees que ya lo estás, intenta reiniciar tu cliente de Minecraft.
velocity.error.player-connection-error=Se ha producido un error interno en tu conexión.
velocity.error.modern-forwarding-needs-new-client=Este servidor solo es compatible con Minecraft 1.13 y superior.
velocity.error.modern-forwarding-failed=El servidor no ha enviado una solicitud de reenvío al proxy. Asegúrate de que tu servidor está configurado para usar el método de reenvío de Velocity.
velocity.error.moved-to-new-server=Has sido echado de {0}\: {1}
velocity.error.no-available-servers=No hay servidores disponibles a los que conectarte. Inténtalo de nuevo más tarde o contacta con un administrador.
velocity.error.illegal-chat-characters=Caracteres no válidos en el chat
# Commands
velocity.command.generic-error=Se ha producido un error al ejecutar este comando.
velocity.command.command-does-not-exist=Este comando no existe.
velocity.command.players-only=Solo los jugadores pueden ejecutar este comando.
velocity.command.server-does-not-exist=El servidor especificado {0} no existe.
velocity.command.player-not-found=El jugador especificado {0} no existe.
velocity.command.server-current-server=Estás conectado a {0}.
velocity.command.server-too-many=Hay demasiados servidores registrados. Usa la finalización con tabulación para ver todos los servidores disponibles.
velocity.command.server-available=Servidores disponibles\:
velocity.command.server-tooltip-player-online={0} jugador conectado
velocity.command.server-tooltip-players-online={0} jugadores conectados
velocity.command.server-tooltip-current-server=Estás conectado a este servidor
velocity.command.server-tooltip-offer-connect-server=Haz clic para conectarte a este servidor
velocity.command.glist-player-singular={0} jugador está conectado al proxy.
velocity.command.glist-player-plural={0} jugadores están conectados al proxy.
velocity.command.glist-view-all=Para ver todos los jugadores por servidores, usa /glist all.
velocity.command.reload-success=La configuración de Velocity ha sido recargada correctamente.
velocity.command.reload-failure=No ha sido posible recargar la configuración de Velocity. Para obtener más información, revisa la consola.
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1} está licenciado bajo los términos de la Licencia Pública General de GNU v3.
velocity.command.no-plugins=Actualmente no hay plugins instalados.
velocity.command.plugins-list=Complementos\: {0}
velocity.command.plugin-tooltip-website=Página web\: {0}
velocity.command.plugin-tooltip-author=Autor\: {0}
velocity.command.plugin-tooltip-authors=Autores\: {0}
velocity.command.dump-uploading=Subiendo la información recopilada...
velocity.command.dump-send-error=Se ha producido un error al comunicarse con los servidores de Velocity. Es posible que los servidores no estén disponibles temporalmente o que exista un problema en tu configuración de red. Puedes encontrar más información en el archivo de registro o la consola de tu servidor Velocity.
velocity.command.dump-success=Se ha creado un informe anónimo que contiene información útil sobre este proxy. Si un desarrollador lo solicita, puedes compartir el siguiente enlace con él\:
velocity.command.dump-will-expire=Este enlace caducará en unos días.
velocity.command.dump-server-error=Se ha producido un error en los servidores de Velocity y la subida no se ha podido completar. Notifica al equipo de Velocity sobre este problema y proporciona los detalles sobre este error disponibles en el archivo de registro o la consola de tu servidor Velocity.
velocity.command.dump-offline=Causa probable\: la configuración DNS del sistema no es válida o no hay conexión a internet
velocity.command.send-usage=/send <jugador> <servidor>
# Kick
velocity.kick.shutdown=El proxy se ha apagado.