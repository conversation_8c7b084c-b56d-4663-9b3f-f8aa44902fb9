# This is the Bukkit help configuration file for Paper.
#
# By default you do not need to modify this file. Help topics for all plugin commands are automatically provided by
# or extracted from your installed plugins. You only need to modify this file if you wish to add new help pages to
# your server or override the help pages of existing plugin commands.
#
# This file is divided up into the following parts:
# -- general-topics: lists admin defined help topics
# -- index-topics:   lists admin defined index topics
# -- amend-topics:   lists topic amendments to apply to existing help topics
# -- ignore-plugins: lists any plugins that should be excluded from help
#
# Examples are given below. When amending command topic, the string <text> will be replaced with the existing value
# in the help topic. Color codes can be used in topic text. The color code character is & followed by 0-F.
# ================================================================
#
# Set this to true to list the individual command help topics in the master help.
# command-topics-in-master-index: true
#
# Each general topic will show up as a separate topic in the help index along with all the plugin command topics.
# general-topics:
#     Rules:
#         shortText: Rules of the server
#         fullText: |
#             &61. Be kind to your fellow players.
#             &B2. No griefing.
#             &D3. No swearing.
#         permission: topics.rules
#
# Each index topic will show up as a separate sub-index in the help index along with all the plugin command topics.
# To override the default help index (displayed when the user executes /help), name the index topic "Default".
# index-topics:
#     Ban Commands:
#         shortText: Player banning commands
#         preamble: Moderator - do not abuse these commands
#         permission: op
#         commands:
#             - /ban
#             - /ban-ip
#             - /banlist
#
# Topic amendments are used to change the content of automatically generated plugin command topics.
# amended-topics:
#     /stop:
#         shortText: Stops the server cold....in its tracks!
#         fullText: <text> - This kills the server.
#         permission: you.dont.have
#
# Any plugin in the ignored plugins list will be excluded from help. The name must match the name displayed by
# the /plugins command. Ignore "Bukkit" to remove the standard bukkit commands from the index. Ignore "All"
# to completely disable automatic help topic generation.
# ignore-plugins:
#    - PluginNameOne
#    - PluginNameTwo
#    - PluginNameThree
#
# If you need help with the configuration or have any questions related to Paper,
# join us in our Discord or check the docs page.
#
# File Reference: https://docs.papermc.io/paper/reference/bukkit-help-configuration/
# Docs: https://docs.papermc.io/
# Discord: https://discord.gg/papermc

{}
