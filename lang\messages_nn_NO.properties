#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=Du er allereie tilkopla denne sørvaren\!
velocity.error.already-connected-proxy=Du er allereie tilkopla denne proxyen\!
velocity.error.already-connecting=Du tilkoplas allereie ein sørvar\!
velocity.error.cant-connect=Kan ikkje kopla til {0}\: {1}
velocity.error.connecting-server-error=Kan ikkje kopla deg til {0}. Prøv gjerne igjen seinare.
velocity.error.connected-server-error=<PERSON> oppkopling mot {0} trefte eit uventa problem.
velocity.error.internal-server-connection-error=Eit internt problem oppstod.
velocity.error.logging-in-too-fast=Du loggar inn for raskt, prøv igjen seinare.
velocity.error.online-mode-only=Du er ikkje innlogga på din Minecraft konto. Dersom du er innlogga, prøv å restarta din Minecraft klient.
velocity.error.player-connection-error=Eit internt problem oppstod.
velocity.error.modern-forwarding-needs-new-client=Denne sørvaren er bare kompatibel med Minecraft 1.13 og nyare.
velocity.error.modern-forwarding-failed=Din server sende ikkje ein vidarekoplingsførespurnad til proxyen. Pass på at sørvaren er konfigurert for Velocity vidarekopling.
velocity.error.moved-to-new-server=Du blei sparka ut frå {0}\: {1}
velocity.error.no-available-servers=Det finst ingen tilgjengelege sørvarar å kopla deg til. Prøv igjen seinare eller kontakt ein administrator.
velocity.error.illegal-chat-characters=Illegal characters in chat
# Commands
velocity.command.generic-error=Ein feil oppstod under utføringa av denne kommandoen.
velocity.command.command-does-not-exist=Denne kommandoen finst ikkje.
velocity.command.players-only=Bare spelarar kan utføra denne kommandoen.
velocity.command.server-does-not-exist=Den oppgjevne sørvaren {0} finst ikkje.
velocity.command.player-not-found=The specified player {0} does not exist.
velocity.command.server-current-server=Du er for augneblinken tilkopla {0}.
velocity.command.server-too-many=Det er for mange sørvarar sette opp. Bruk tabfullføring for å sjå alle tilgjengelege sørvarar.
velocity.command.server-available=Tilgjengelege sørvarar\:
velocity.command.server-tooltip-player-online={0} spelar tilkopla
velocity.command.server-tooltip-players-online={0} spelarar tilkopla
velocity.command.server-tooltip-current-server=Du er for augneblinken tilkopla denne sørvaren
velocity.command.server-tooltip-offer-connect-server=Trykk for å kopla til denne sørvaren
velocity.command.glist-player-singular={0} spelar er for augneblinken tilkopla denne proxyen.
velocity.command.glist-player-plural={0} spelare er for augneblinken tilkopla denne proxyen.
velocity.command.glist-view-all=For å sjå alle tilkopla spelarar, gjer /glist all.
velocity.command.reload-success=Velocity konfigurasjonen blei lasta inn på nytt.
velocity.command.reload-failure=Kan ikkje lasta inn Velocity konfigurasjonen din. Sjekk konsollen for meir detaljar.
velocity.command.version-copyright=Opphavsrett 2018-2023 {0}. {1} er lisensiert under vilkåra av GNU General Public License v3.
velocity.command.no-plugins=Det er ingen plugins installert.
velocity.command.plugins-list=Plugins\: {0}
velocity.command.plugin-tooltip-website=Heimeside\: {0}
velocity.command.plugin-tooltip-author=Skapar\: {0}
velocity.command.plugin-tooltip-authors=Skaparar\: {0}
velocity.command.dump-uploading=Uploading gathered information...
velocity.command.dump-send-error=An error occurred while communicating with the Velocity servers. The servers may be temporarily unavailable or there is an issue with your network settings. You can find more information in the log or console of your Velocity server.
velocity.command.dump-success=Created an anonymised report containing useful information about this proxy. If a developer requested it, you may share the following link with them\:
velocity.command.dump-will-expire=This link will expire in a few days.
velocity.command.dump-server-error=An error occurred on the Velocity servers and the dump could not be completed. Please contact the Velocity staff about this problem and provide the details about this error from the Velocity console or server log.
velocity.command.dump-offline=Likely cause\: Invalid system DNS settings or no internet connection
velocity.command.send-usage=/send <player> <server>
# Kick
velocity.kick.shutdown=Proxy shutting down.