#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=Du er allerede tilsluttet til den server\!
velocity.error.already-connected-proxy=Du er allerede tilsluttet til proxyen\!
velocity.error.already-connecting=Du forsøger allerede at oprette forbindelse til en server\!
velocity.error.cant-connect=Kan ikke forbinde til {0}\: {1}
velocity.error.connecting-server-error=Kan ikke forbinde dig til {0}. Prøv igen senere.
velocity.error.connected-server-error=<PERSON> forbindelse til {0} stødte på et problem.
velocity.error.internal-server-connection-error=Der opstod en intern server forbindelsesfejl.
velocity.error.logging-in-too-fast=Du logger ind for hurtigt, prøv igen senere.
velocity.error.online-mode-only=Du er ikke logget ind på din Minecraft-konto. Hvis du er logget ind på din Minecraft-konto, så prøv at genstarte din Minecraft-klient.
velocity.error.player-connection-error=Der opstod en intern fejl i din forbindelse.
velocity.error.modern-forwarding-needs-new-client=Denne server er kun kompatibel med Minecraft 1.13 og derover.
velocity.error.modern-forwarding-failed=Din server sendte ikke en viderestillingsanmodning til proxyen. Sørg for, at serveren er konfigureret til Velocity forwarding.
velocity.error.moved-to-new-server=Du blev smidt ud fra {0}\: {1}
velocity.error.no-available-servers=Der er ingen tilgængelige servere at forbinde dig til. Prøv igen senere eller kontakt en administrator.
velocity.error.illegal-chat-characters=Illegal characters in chat
# Commands
velocity.command.generic-error=Der opstod en fejl da du kørte kommandoen.
velocity.command.command-does-not-exist=Denne kommando eksisterer ikke.
velocity.command.players-only=Kun spillere kan køre denne kommando.
velocity.command.server-does-not-exist=Den angivne server {0} findes ikke.
velocity.command.player-not-found=The specified player {0} does not exist.
velocity.command.server-current-server=Du er i øjeblikket forbundet til {0}.
velocity.command.server-too-many=Der er sat for mange servere op. Brug tab færdiggørelse til at se alle tilgængelige servere.
velocity.command.server-available=Tilgængelige servere\:
velocity.command.server-tooltip-player-online={0} spiller online
velocity.command.server-tooltip-players-online={0} spillere online
velocity.command.server-tooltip-current-server=I øjeblikket forbundet til serveren
velocity.command.server-tooltip-offer-connect-server=Klik for at forbinde til denne server
velocity.command.glist-player-singular={0} spiller er i øjeblikket forbundet til proxyen.
velocity.command.glist-player-plural={0} spillere er i øjeblikket forbundet til proxyen.
velocity.command.glist-view-all=For at se alle spillere på servere, brug /glist all.
velocity.command.reload-success=Velocity konfiguration blev genindlæst.
velocity.command.reload-failure=Kan ikke genindlæse din Velocity konfiguration. Tjek konsollen for flere detaljer.
velocity.command.version-copyright=Ophavsret 2018-2023 {0}. {1} er licenseret under betingelserne i GNU General Public License v3.
velocity.command.no-plugins=Der er ingen plugins installeret i øjeblikket.
velocity.command.plugins-list=Plugins\: {0}
velocity.command.plugin-tooltip-website=Hjemmeside\: {0}
velocity.command.plugin-tooltip-author=Forfatter\: {0}
velocity.command.plugin-tooltip-authors=Skabere\: {0}
velocity.command.dump-uploading=Uploader indsamlet information...
velocity.command.dump-send-error=Der opstod en fejl under kommunikation med Velocity serverne. Serverne kan være midlertidigt utilgængelige, eller der er et problem med dine netværksindstillinger. Du kan finde mere information i loggen eller konsollen på din Velocity server.
velocity.command.dump-success=Oprettet en anonymiseret rapport med nyttige oplysninger om denne proxy. Hvis en udvikler anmodede om det, kan du dele følgende link med dem\:
velocity.command.dump-will-expire=Dette link udløber om et par dage.
velocity.command.dump-server-error=Der opstod en fejl på Velocity serverne og udskrivningen af fejl logbogen kunne ikke fuldføres. Kontakt venligst Velocity personalet om dette problem og giv oplysninger om denne fejl fra Velocity konsollen eller server loggen.
velocity.command.dump-offline=Sandsynlig årsag\: Ugyldig system DNS indstillinger eller ingen internetforbindelse
velocity.command.send-usage=/send <player> <server>
# Kick
velocity.kick.shutdown=Proxy lukker ned.