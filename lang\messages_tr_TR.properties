#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=Bu sunucuya zaten bağlısın\!
velocity.error.already-connected-proxy=Bu sunucuya zaten bağlısın\!
velocity.error.already-connecting=Bu sunucuya zaten bağlanmaya çalışıyorsun\!
velocity.error.cant-connect={0}\:{1} ile bağlantı kurulamadı
velocity.error.connecting-server-error=Seni {0}'ye bağlayamadık. Lütfen daha sonra tekrar deneyiniz.
velocity.error.connected-server-error={0} ile bağlantında bir problem meydana geldi.
velocity.error.internal-server-connection-error=Dahili sunucu bağlantısında bir hata meydana geldi.
velocity.error.logging-in-too-fast=Çok hızlı bağlanmaya çalışıyorsun, daha sonra tekrar dene.
velocity.error.online-mode-only=Minecraft hesabına bağlı değilsin. Eğer Minecraft hesabına bağlıysan, Minecraft istemcini kapatıp tekrardan başlat.
velocity.error.player-connection-error=Bağlantında dahili bir hata meydana geldi.
velocity.error.modern-forwarding-needs-new-client=Bu sunucuya Minecraft'ın sadece 1.13 ve üzeri sürümleri ile giriş yapabilirsin.
velocity.error.modern-forwarding-failed=Sunucun Velocity'ye yönlendirme isteğinde bulunmadı. Sunucunun Velocity'nin ayarlarında ayarlandığına emin ol.
velocity.error.moved-to-new-server={0}\:{1} sunucusundan atıldınız
velocity.error.no-available-servers=Seni bağlayabileceğimiz bir sunucu bulamadık. Lütfen daha sonra tekrar dene veya bir yetkili ile iletişime geç.
velocity.error.illegal-chat-characters=Illegal characters in chat
# Commands
velocity.command.generic-error=Bu komutu çalıştırırken bir hata meydana geldi.
velocity.command.command-does-not-exist=Böyle bir komut yok.
velocity.command.players-only=Sadece oyuncular bu komutu çalıştırabilir.
velocity.command.server-does-not-exist=Belirlenmiş sunucu {0} bulunmuyor.
velocity.command.player-not-found=The specified player {0} does not exist.
velocity.command.server-current-server=Şu anda {0} sunucusuna bağlısın.
velocity.command.server-too-many=Ayarlanmış birçok sunucu mevcut. Mevcut bütün sunucuları görmek için tamamlama özelliğini kullan.
velocity.command.server-available=Müsait sunucular\:
velocity.command.server-tooltip-player-online={0} oyuncu çevrimiçi
velocity.command.server-tooltip-players-online={0} oyuncu çevrimiçi
velocity.command.server-tooltip-current-server=Şu anda bu sunucuya bağlısın
velocity.command.server-tooltip-offer-connect-server=Bu sunucuya bağlanmak için tıkla
velocity.command.glist-player-singular=Şu anda sunucuya toplam {0} oyuncu bağlı.
velocity.command.glist-player-plural=Şu anda sunucuya toplam {0} oyuncu bağlı.
velocity.command.glist-view-all=Sunucudaki bütün oyuncuları görüntülemek için /glist all komutunu kullan.
velocity.command.reload-success=Velocity ayarları başarıyla güncellendi.
velocity.command.reload-failure=Velocity ayarlarınız güncellenemiyor. Daha fazla bilgi için konsolu kontrol edin.
velocity.command.version-copyright=Talif hakkı 2018-2023 {0}. {1}, GNU General Public License v3 adı altında lisanslanmıştır.
velocity.command.no-plugins=Yüklenmiş herhangi bir eklenti yok.
velocity.command.plugins-list=Eklentiler\: {0}
velocity.command.plugin-tooltip-website=Website\: {0}
velocity.command.plugin-tooltip-author=Yapımcı\: {0}
velocity.command.plugin-tooltip-authors=Yapımcılar\: {0}
velocity.command.dump-uploading=Toplanılan bilgiler yükleniyor...
velocity.command.dump-send-error=Velocity sunucuları ile iletişim sırasında bir hata oluştu. Sunucular geçici olarak kullanılamıyor olabilir veya ağ ayarlarınızla ilgili bir sorun olabilir. Velocity sunucunuzun logunda veya konsolunda daha fazla bilgi bulabilirsiniz.
velocity.command.dump-success=Bu proxy hakkında faydalı bilgiler içeren anonim bir rapor oluşturdu. Bir geliştirici talep ettiyse, aşağıdaki bağlantıyı onlarla paylaşabilirsiniz\:
velocity.command.dump-will-expire=Bu bağlantı birkaç gün içinde geçersiz olacak.
velocity.command.dump-server-error=Velocity sunucularında bir hata oluştu ve döküm tamamlanamadı. Lütfen bu sorunla ilgili olarak Velocity ekibiyle iletişime geçin ve bu hatayla ilgili ayrıntıları Velocity konsolundan veya sunucu logundan sağlayın.
velocity.command.dump-offline=Olası neden\: Geçersiz sistem DNS ayarları veya internet bağlantısı yok
velocity.command.send-usage=/send <player> <server>
# Kick
velocity.kick.shutdown=Proxy kapatılıyor.