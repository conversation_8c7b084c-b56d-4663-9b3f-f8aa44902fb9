#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=Вы уже подключены к этому серверу\!
velocity.error.already-connected-proxy=Игрок с таким ником уже играет на сервере\!
velocity.error.already-connecting=Вы уже подключаетесь к серверу\!
velocity.error.cant-connect=Не удалось подключиться к серверу {0}\: {1}
velocity.error.connecting-server-error=Не удалось подключить вас к серверу {0}. Пожалуйста, попробуйте снова через некоторое время.
velocity.error.connected-server-error=С вашим подключением к серверу {0} возникла проблема.
velocity.error.internal-server-connection-error=На сервере произошла внутренняя ошибка подключения.
velocity.error.logging-in-too-fast=Вы входите слишком быстро, попробуйте снова через некоторое время.
velocity.error.online-mode-only=Вы не вошли в свой аккаунт Minecraft. Если вы уверены, что вошли в аккаунт, попробуйте перезапустить свой клиент Minecraft.
velocity.error.player-connection-error=В вашем подключении произошла внутренняя ошибка.
velocity.error.modern-forwarding-needs-new-client=Этот сервер совместим только с Minecraft 1.13 и выше.
velocity.error.modern-forwarding-failed=Ваш сервер не посылал запрос на переадресацию на прокси-сервер. Убедитесь, что сервер настроен на переадресацию Velocity.
velocity.error.moved-to-new-server=Вы были кикнуты с сервера {0}\: {1}
velocity.error.no-available-servers=Нет серверов, доступных для подключения. Попробуйте позже или свяжитесь с администратором.
velocity.error.illegal-chat-characters=Недопустимые символы в чате
# Commands
velocity.command.generic-error=Во время выполнения этой команды произошла ошибка.
velocity.command.command-does-not-exist=Этой команды не существует.
velocity.command.players-only=Только игроки могут использовать эту команду.
velocity.command.server-does-not-exist=Указанный сервер {0} не существует.
velocity.command.player-not-found=Указанный игрок {0} не существует.
velocity.command.server-current-server=На данный момент вы подключены к серверу {0}.
velocity.command.server-too-many=Настроено слишком много серверов. Для просмотра всех доступных серверов, используйте автозаполнение клавишей Tab.
velocity.command.server-available=Доступные серверы\:
velocity.command.server-tooltip-player-online={0} игрок онлайн
velocity.command.server-tooltip-players-online={0} игрок(а, ов) онлайн
velocity.command.server-tooltip-current-server=Подключен к этому серверу
velocity.command.server-tooltip-offer-connect-server=Кликните, чтобы присоединиться к этому серверу
velocity.command.glist-player-singular={0} игрок подключен к прокси на данный момент.
velocity.command.glist-player-plural={0} игрок(а, ов) подключены к прокси на данный момент.
velocity.command.glist-view-all=Чтобы просмотреть всех игроков на серверах, используйте /glist all.
velocity.command.reload-success=Конфигурация Velocity успешно перезагружена.
velocity.command.reload-failure=Не удалось перезагрузить конфигурацию Velocity. Проверьте консоль для получения более подробной информации.
velocity.command.version-copyright=Copyright 2018-2023 {0}. {1} лицензирована на условиях GNU General Public License v3.
velocity.command.no-plugins=Ни одного плагина не установлено.
velocity.command.plugins-list=Плагины\: {0}
velocity.command.plugin-tooltip-website=Веб-сайт\: {0}
velocity.command.plugin-tooltip-author=Автор\: {0}
velocity.command.plugin-tooltip-authors=Авторы\: {0}
velocity.command.dump-uploading=Загрузка полученной информации...
velocity.command.dump-send-error=Произошла ошибка при попытке связаться с серверами Velocity. Возможно, серверы временно недоступны или ваша сеть настроена неправильно. Более подробную информацию можно найти в логах или консоли вашего сервера Velocity.
velocity.command.dump-success=Создан анонимный отчет, содержащий полезную информацию об этом прокси. Если этот отчёт запросил разработчик, вы можете поделиться с ним следующей ссылкой\:
velocity.command.dump-will-expire=Срок действия этой ссылки истечёт через несколько дней.
velocity.command.dump-server-error=Не удалось завершить дамп, так как на серверах Velocity произошла ошибка. Пожалуйста, свяжитесь с командой Velocity по поводу этой проблемы и сообщите подробности, а также предоставьте подробную информацию об этой ошибке из консоли Velocity или логов сервера.
velocity.command.dump-offline=Вероятная причина\: Неверные настройки DNS или отсутствие подключения к интернету
velocity.command.send-usage=/send <никнейм> <сервер>
# Kick
velocity.kick.shutdown=Прокси-сервер выключается.