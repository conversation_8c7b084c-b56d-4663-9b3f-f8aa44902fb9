#
# Copyright (C) 2018 Velocity Contributors
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <https://www.gnu.org/licenses/>.
#

velocity.error.already-connected=您已经连接到此服务器了！
velocity.error.already-connected-proxy=您已经连接到此代理服务器了！
velocity.error.already-connecting=您已经在尝试连接服务器了！
velocity.error.cant-connect=无法连接至 {0}：{1}
velocity.error.connecting-server-error=无法将您连接至 {0}，请稍后再试。
velocity.error.connected-server-error=您与 {0} 的连接出现问题。
velocity.error.internal-server-connection-error=发送内部服务器连接错误。
velocity.error.logging-in-too-fast=您的登录过于频繁，请稍后重试。
velocity.error.online-mode-only=您尚未登录至 Minecraft 账户。若您已登录，请尝试重启您的客户端。
velocity.error.player-connection-error=您的连接发生内部错误。
velocity.error.modern-forwarding-needs-new-client=此服务器仅兼容 Minecraft 1.13 及更高版本。
velocity.error.modern-forwarding-failed=您的服务器未向代理服务器转发请求，请确保已配置 Velocity 转发。
velocity.error.moved-to-new-server=您已被踢出 {0}：{1}
velocity.error.no-available-servers=您当前没有可连接的服务器，请稍后重试或联系管理员。
velocity.error.illegal-chat-characters=聊天中出现非法字符
# Commands
velocity.command.generic-error=执行此命令时发生错误。
velocity.command.command-does-not-exist=此命令不存在。
velocity.command.players-only=只有玩家才能执行此命令。
velocity.command.server-does-not-exist=指定的服务器 {0} 不存在。
velocity.command.player-not-found=指定的玩家 {0} 不存在。
velocity.command.server-current-server=您已连接至 {0}。
velocity.command.server-too-many=设置的服务器过多，请使用 Tab 键补全来查看所有可用的服务器。
velocity.command.server-available=可用的服务器：
velocity.command.server-tooltip-player-online={0} 名玩家在线
velocity.command.server-tooltip-players-online={0} 名玩家在线
velocity.command.server-tooltip-current-server=当前已连接至此服务器
velocity.command.server-tooltip-offer-connect-server=点击连接至此服务器
velocity.command.glist-player-singular=共有 {0} 名玩家已连接至此代理服务器。
velocity.command.glist-player-plural=共有 {0} 名玩家已连接至此代理服务器。
velocity.command.glist-view-all=使用 /glist all 命令来查看所有服务器的全部玩家列表。
velocity.command.reload-success=Velocity 配置已成功重载。
velocity.command.reload-failure=无法重载 Velocity 配置，请查看控制台了解详情。
velocity.command.version-copyright=Copyright 2018-2023 {0}。{1} 以 GNU 通用公共许可证第三版授权。
velocity.command.no-plugins=当前没有安装任何插件。
velocity.command.plugins-list=插件：{0}
velocity.command.plugin-tooltip-website=网站：{0}
velocity.command.plugin-tooltip-author=作者：{0}
velocity.command.plugin-tooltip-authors=作者：{0}
velocity.command.dump-uploading=正在上传已收集的信息...
velocity.command.dump-send-error=与 Velocity 服务器通信时发生错误，服务器可能暂不可用或您的网络设置存在问题。您可在 Velocity 服务器日志或控制台中了解详情。
velocity.command.dump-success=已创建关于此代理的匿名反馈数据。若开发者需要，您可与其分享以下链接：
velocity.command.dump-will-expire=此链接将于几天后过期。
velocity.command.dump-server-error=Velocity 服务器发生错误，无法完成内存转储。请联系 Velocity 开发者并从 Velocity 控制台或日志中提供详细错误信息。
velocity.command.dump-offline=可能原因：系统 DNS 设置无效或无网络连接
velocity.command.send-usage=/send <玩家> <服务器>
# Kick
velocity.kick.shutdown=正在关闭代理。